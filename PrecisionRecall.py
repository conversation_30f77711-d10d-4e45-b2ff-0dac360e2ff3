import os
import random
from typing import List, Literal, Union, Set
 
import autoevals
from datasets import load_dataset
import braintrust
import openai
from pydantic import BaseModel, Field, create_model
 
# Uncomment if you want to hardcode your API keys
# os.environ["BRAINTRUST_API_KEY"] = "YOUR_BRAINTRUST_API_KEY"
# os.environ["OPENAI_API_KEY"] = "YOUR_OPENAI_API_KEY"
 
openai_client = braintrust.wrap_openai(openai.OpenAI())

EMOTIONS = [
    "admiration",
    "amusement",
    "anger",
    "annoyance",
    "approval",
    "caring",
    "confusion",
    "curiosity",
    "desire",
    "disappointment",
    "disapproval",
    "disgust",
    "embarrassment",
    "excitement",
    "fear",
    "gratitude",
    "grief",
    "joy",
    "love",
    "nervousness",
    "optimism",
    "pride",
    "realization",
    "relief",
    "remorse",
    "sadness",
    "surprise",
    "neutral",
]
 
EmotionType = Literal[tuple(EMOTIONS)]
 
EmotionClassification = create_model(
    "EmotionClassification", emotions=(List[EmotionType], ...)
)
 
 
def load_data(limit: int = 100):
    ds = load_dataset("google-research-datasets/go_emotions", "raw")
    for i, item in list(enumerate(ds["train"]))[:limit]:
        actual_emotions = [emotion for emotion in EMOTIONS if item.get(emotion, 0) == 1]
        yield {
            "input": item["text"],
            "expected": actual_emotions,
            "metadata": {"subreddit": item["subreddit"], "author": item["author"]},
        }

def llm_classifier(text: str) -> EmotionClassification:
    prompt = (
        f"Analyze the emotional content in this text and STRICTLY classify it using ONLY the following emotion labels:\n"
        f"{', '.join(EMOTIONS)}\n\n"
        f"IMPORTANT: You must ONLY use emotions from the above list. Do not use any other emotion labels and DO NOT repeat emotions.\n\n"
        f"Text: {text}\n\n"
        f"Respond with a JSON object containing:\n"
        f"- emotions: array of emotions from the provided list only\n"
        f"Remember: Only use emotions from the provided list. If you see an emotion that isn't in the list, map it to the closest matching emotion from the list."
    )
 
    response = openai_client.beta.chat.completions.parse(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        temperature=0,
        response_format=EmotionClassification,
    )
 
    result = response.choices[0].message.content
    return EmotionClassification.model_validate_json(result)
 
 
def random_classifier(text: str) -> EmotionClassification:
    num_emotions = random.randint(1, 3)
    selected_emotions = random.sample(EMOTIONS, num_emotions)
    return EmotionClassification(
        emotions=selected_emotions,
        confidence=random.random(),
        rationale="Random selection",
    )

def emotion_precision(
    output: EmotionClassification, expected: List[EmotionType]
) -> float:
    expected_set = set(expected)
    output_set = set(output.emotions)
    true_positives = len(output_set & expected_set)
    false_positives = len(output_set - expected_set)
    return (
        true_positives / (true_positives + false_positives)
        if (true_positives + false_positives) > 0
        else 1.0
    )
 
 
def emotion_recall(output: EmotionClassification, expected: List[EmotionType]) -> float:
    expected_set = set(expected)
    output_set = set(output.emotions)
    true_positives = len(output_set & expected_set)
    false_negatives = len(expected_set - output_set)
    return (
        true_positives / (true_positives + false_negatives)
        if (true_positives + false_negatives) > 0
        else 1.0
    )
 
 
def emotion_f1(output: EmotionClassification, expected: List[EmotionType]) -> float:
    prec = emotion_precision(output, expected)
    rec = emotion_recall(output, expected)
    return 2 * (prec * rec) / (prec + rec) if (prec + rec) > 0 else 0.0

def run_evaluations(num_samples: int = 100):
    data = list(load_data(limit=num_samples))
 
    braintrust.Eval(
        "pedro-emotion-classification-cookbook-main",
        data=data,  # Return the preloaded data
        task=random_classifier,
        scores=[emotion_precision, emotion_recall, emotion_f1],
        metadata={"classifier_type": "random"},
        experiment_name="random-classifier",
    )
 
    braintrust.Eval(
        "pedro-emotion-classification-cookbook-main",
        data=data,  # Return the preloaded data
        task=llm_classifier,
        scores=[emotion_precision, emotion_recall, emotion_f1],
        metadata={"classifier_type": "llm", "model": "gpt-4o"},
        experiment_name="llm-classifier",
    )
 
 
if __name__ == "__main__":
    run_evaluations(num_samples=100)  # Adjust the number of samples as needed