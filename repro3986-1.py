#!/usr/bin/env python3
"""
Script to replicate the specific JSONDecodeError:
json.decoder.JSONDecodeError: Invalid control character at: line 1 column 321 (char 320)

This script demonstrates the exact error that occurs in autoevals.LLMClassifier
when OpenAI returns JSON with unescaped control characters.
"""

import json
import sys
import os

# Add the py directory to the path so we can import autoevals
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'py'))

from autoevals.llm import OpenAILLMClassifier

def create_problematic_json():
    """
    Create a JSON string that will cause the specific error:
    Invalid control character at: line 1 column 321 (char 320)
    """
    
    # Create a JSON string that's approximately 320 characters long
    # and then has a control character (newline) at position ~321
    base_text = (
        'This is a long reason that goes on for a while to reach approximately '
        'character position 320 where we will have a control character issue. '
        'The issue occurs when the LLM generates text that contains unescaped '
        'control characters like this'
    )
    
    # Add a newline character that will cause the error
    problematic_text = base_text + '\nnewline character which causes the JSON parser to fail.'
    
    # Create the full JSON string
    json_str = json.dumps({
        "choice": "A", 
        "reasons": [problematic_text]
    })
    
    # Now manually insert an unescaped newline to simulate what OpenAI might return
    # We'll replace the escaped newline with an actual newline character
    problematic_json = json_str.replace('\\n', '\n')
    
    return problematic_json

def replicate_error_with_standard_json():
    """Demonstrate the error with standard json.loads()"""
    
    print("=== Replicating the JSONDecodeError ===\n")
    
    problematic_json = create_problematic_json()
    
    print(f"JSON string length: {len(problematic_json)} characters")
    print(f"Looking for control character position...")
    
    # Find the position of the first unescaped newline
    newline_pos = problematic_json.find('\n')
    print(f"First newline character found at position: {newline_pos}")
    
    print(f"\nJSON preview (first 100 chars): {repr(problematic_json[:100])}")
    print(f"JSON around position 320: {repr(problematic_json[310:330])}")
    
    print("\nAttempting to parse with standard json.loads()...")
    try:
        result = json.loads(problematic_json)
        print("❌ Unexpected: json.loads() succeeded (this shouldn't happen)")
    except json.JSONDecodeError as e:
        print(f"✅ Expected error occurred: {e}")
        print(f"Error position: {e.pos}")
        print(f"Error message: {e.msg}")
        return e

def replicate_error_with_llm_classifier():
    """Replicate the error as it would occur in LLMClassifier"""
    
    print("\n=== Replicating error in LLMClassifier context ===\n")
    
    # Create a mock LLMClassifier
    classifier = OpenAILLMClassifier(
        name="test_classifier",
        messages=[{"role": "user", "content": "Test prompt"}],
        model="gpt-4",
        choice_scores={"A": 1, "B": 0.5, "C": 0},
        classification_tools=[],
    )
    
    # Create a mock OpenAI response that would cause the error
    problematic_json = create_problematic_json()
    
    mock_response = {
        "tool_calls": [{
            "function": {
                "name": "select_choice",
                "arguments": problematic_json
            }
        }]
    }
    
    print("Mock OpenAI response created with problematic JSON in tool call arguments")
    print(f"Arguments length: {len(problematic_json)} characters")
    print(f"Arguments preview: {repr(problematic_json[:100])}")
    
    print("\nAttempting to process response with LLMClassifier._process_response()...")
    try:
        result = classifier._process_response(mock_response)
        print("❌ Unexpected: LLMClassifier succeeded (this shouldn't happen with original code)")
        print(f"Result: {result}")
    except json.JSONDecodeError as e:
        print(f"✅ Expected JSONDecodeError occurred: {e}")
        print(f"Error position: {e.pos}")
        print(f"This is the exact error you would see when using autoevals.LLMClassifier")
        return e
    except Exception as e:
        print(f"❌ Different error occurred: {type(e).__name__}: {e}")
        return e

def create_exact_321_position_error():
    """Create a JSON string with control character exactly at position 321"""
    
    print("\n=== Creating exact position 321 error ===\n")
    
    # Build a string that will have a control character at exactly position 321
    prefix = '{"choice": "A", "reasons": ["'
    suffix = '"]}'
    
    # Calculate how much text we need to reach position 321
    target_pos = 321
    current_length = len(prefix)
    needed_length = target_pos - current_length - 1  # -1 for the newline we'll insert
    
    # Create filler text
    filler_text = "x" * needed_length
    
    # Construct the problematic JSON
    problematic_json = prefix + filler_text + '\n' + "text after newline" + suffix
    
    print(f"Created JSON with length: {len(problematic_json)}")
    print(f"Newline position: {problematic_json.find(chr(10))}")
    print(f"Character at position 321: {repr(problematic_json[320]) if len(problematic_json) > 320 else 'N/A'}")
    
    print("\nAttempting to parse...")
    try:
        result = json.loads(problematic_json)
        print("❌ Unexpected success")
    except json.JSONDecodeError as e:
        print(f"✅ JSONDecodeError at position {e.pos}: {e}")
        return e

if __name__ == "__main__":
    print("🔍 Replicating JSONDecodeError: Invalid control character at: line 1 column 321 (char 320)")
    print("=" * 80)
    
    # Test 1: Show the error with standard json.loads
    error1 = replicate_error_with_standard_json()
    
    # Test 2: Show the error in LLMClassifier context  
    error2 = replicate_error_with_llm_classifier()
    
    # Test 3: Create exact position 321 error
    error3 = create_exact_321_position_error()
    
    print("\n" + "=" * 80)
    print("🎯 SUMMARY:")
    print("This script demonstrates the exact JSONDecodeError that occurs when")
    print("autoevals.LLMClassifier tries to parse OpenAI tool call responses")
    print("that contain unescaped control characters (like newlines, tabs, etc.)")
    print("\nThe error happens in the _process_response method at line:")
    print("    args = json.loads(tool_call['function']['arguments'])")
    print("\nTo fix this, the json.loads() call should be replaced with a")
    print("safe JSON parser that can handle control characters.")
